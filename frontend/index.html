<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>User Order Portal</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      background: #f8f9fa;
    }
    header {
      background: #232f3e;
      color: white;
      padding: 1rem;
      text-align: center;
      font-size: 1.5rem;
      font-weight: bold;
    }
    .container {
      display: flex;
      padding: 1rem;
      gap: 1rem;
    }
    .products {
      flex: 3;
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
      gap: 1rem;
    }
    .product {
      background: white;
      padding: 1rem;
      border-radius: 8px;
      text-align: center;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .product img {
      max-width: 100%;
      height: 150px;
      object-fit: cover;
    }
    .product h3 {
      font-size: 1rem;
      margin: 0.5rem 0;
    }
    .product p {
      color: #b12704;
      font-weight: bold;
    }
    .product button {
      background: #ff9900;
      border: none;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
    }
    .cart {
      flex: 1;
      background: white;
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .cart h2 {
      font-size: 1.2rem;
      margin-bottom: 1rem;
    }
    .cart-item {
      display: flex;
      justify-content: space-between;
      margin: 0.5rem 0;
    }
    .place-order {
      background: #ffa41c;
      border: none;
      width: 100%;
      padding: 0.8rem;
      border-radius: 5px;
      cursor: pointer;
      font-weight: bold;
      margin-top: 1rem;
    }
    .confirmation {
      text-align: center;
      font-size: 1.2rem;
      font-weight: bold;
      color: green;
      margin-top: 2rem;
    }
    /* Track Order Section */
    .track-section {
      margin: 2rem;
      padding: 1rem;
      background: white;
      border-radius: 8px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    .progress-container {
      display: flex;
      justify-content: space-between;
      margin: 2rem 0;
      position: relative;
    }
    .progress-container::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 100%;
      height: 4px;
      background: #ccc;
      transform: translateY(-50%);
      z-index: 0;
    }
    .step {
      position: relative;
      z-index: 1;
      background: #ccc;
      color: white;
      padding: 0.5rem 1rem;
      border-radius: 20px;
      font-size: 0.9rem;
    }
    .step.completed {
      background: #ff9900;
    }
    .tracking-log {
      margin-top: 1rem;
    }
    .tracking-log li {
      margin: 0.5rem 0;
    }
  </style>
</head>
<body>
  <header>User Order Portal</header>

  <!-- Place Order Section -->
  <div class="container" id="order-section">
    <div class="products" id="product-list"></div>
    <div class="cart">
      <h2>Shopping Cart</h2>
      <div id="cart-items"></div>
      <p><strong>Total: ₹<span id="total">0</span></strong></p>
      <button class="place-order" onclick="placeOrder()">Place Order</button>
    </div>
  </div>

  <!-- Order Confirmation -->
  <div class="confirmation" id="confirmation" style="display:none;"></div>

  <!-- Track Order Section -->
  <div class="track-section">
    <h2>Track Your Order</h2>
    <input type="number" id="trackId" placeholder="Enter Order ID"/>
    <button onclick="trackOrder()">Track Order</button>

    <div class="progress-container" id="progress-bar"></div>
    <ul class="tracking-log" id="tracking-log"></ul>
  </div>

  <script>
    const API_URL = "http://127.0.0.1:5001"; // Flask API backend

    const products = [
      { id: 1, name: "High-Performance Laptop", price: 108999, img: "https://via.placeholder.com/200?text=Laptop" },
      { id: 2, name: "Wireless Bluetooth Headphones", price: 20999, img: "https://via.placeholder.com/200?text=Headphones" },
      { id: 3, name: "Smartphone Pro Max", price: 83999, img: "https://via.placeholder.com/200?text=Smartphone" },
      { id: 4, name: "RGB Gaming Keyboard", price: 5999, img: "https://via.placeholder.com/200?text=Keyboard" },
      { id: 5, name: "Ergonomic Mouse", price: 2999, img: "https://via.placeholder.com/200?text=Mouse" },
      { id: 6, name: "HD Tablet", price: 15999, img: "https://via.placeholder.com/200?text=Tablet" },
    ];

    let cart = [];

    // Render Products
    const productList = document.getElementById("product-list");
    products.forEach(p => {
      const div = document.createElement("div");
      div.className = "product";
      div.innerHTML = `
        <img src="${p.img}" alt="${p.name}">
        <h3>${p.name}</h3>
        <p>₹${p.price.toLocaleString()}</p>
        <button onclick="addToCart(${p.id})">Add to Cart</button>
      `;
      productList.appendChild(div);
    });

    function addToCart(id) {
      const product = products.find(p => p.id === id);
      cart.push(product);
      renderCart();
    }

    function renderCart() {
      const cartItems = document.getElementById("cart-items");
      cartItems.innerHTML = "";
      let total = 0;
      cart.forEach((item, i) => {
        total += item.price;
        cartItems.innerHTML += `
          <div class="cart-item">
            <span>${item.name}</span>
            <span>₹${item.price.toLocaleString()}</span>
          </div>
        `;
      });
      document.getElementById("total").textContent = total.toLocaleString();
    }

    async function placeOrder() {
      if (cart.length === 0) return alert("Cart is empty!");

      const amount = cart.reduce((sum, i) => sum + i.price, 0);
      const items = cart.map(i => i.name);

      const response = await fetch(`${API_URL}/place-order`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ user_id: "USER_123", items, amount })
      });

      const data = await response.json();
      if (response.ok) {
        document.getElementById("order-section").style.display = "none";
        document.getElementById("confirmation").style.display = "block";
        document.getElementById("confirmation").textContent =
          `✅ Order Placed Successfully! Your Order ID is: ${data.order_id}`;
      } else {
        alert("Failed to place order");
      }
    }

    async function trackOrder() {
      const orderId = document.getElementById("trackId").value;
      if (!orderId) return alert("Enter a valid Order ID");

      const response = await fetch(`${API_URL}/order-status/${orderId}`);
      const data = await response.json();

      if (!response.ok) {
        alert(data.error || "Order not found");
        return;
      }

      const stages = ["Placed", "Packed", "Shipped", "Delivered"];
      const progressBar = document.getElementById("progress-bar");
      progressBar.innerHTML = "";
      stages.forEach(stage => {
        const div = document.createElement("div");
        div.className = "step";
        if (stages.indexOf(stage) <= stages.indexOf(data.status)) {
          div.classList.add("completed");
        }
        div.textContent = stage;
        progressBar.appendChild(div);
      });

      // Simulated tracking log
      const log = document.getElementById("tracking-log");
      log.innerHTML = "";
      stages.forEach((stage, i) => {
        if (i <= stages.indexOf(data.status)) {
          log.innerHTML += `<li>${stage} - ${new Date().toLocaleString()}</li>`;
        }
      });
    }
  </script>
</body>
</html>
