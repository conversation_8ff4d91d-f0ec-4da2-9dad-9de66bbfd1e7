// src/components/ProductCard.jsx

import React from 'react';

export default function ProductCard({ product, onAddToCart }) {
  // Format price to Indian Rupees
  const formattedPrice = new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 0, // Ensure no decimal places
    maximumFractionDigits: 0
  }).format(product.price);

  return (
    <div className="
      bg-white p-4 rounded-xl shadow-md
      hover:shadow-xl hover:scale-105 transition-all duration-300 ease-in-out
      flex flex-col items-center text-center
    ">
      <img
        src={product.image}
        alt={product.name}
        className="w-full h-40 object-contain mb-4 rounded-lg"
      />
      <h3 className="font-semibold text-lg text-gray-800 mb-1">{product.name}</h3>
      {/* --- MODIFIED LINE BELOW --- */}
      <p className="text-xl font-bold text-red-700 mb-4">{formattedPrice}</p> {/* Changed text-gray-900 to text-red-700 */}
      {/* --- MODIFIED LINE ABOVE --- */}
      <button
        onClick={() => onAddToCart(product)}
        className="
          bg-amber-500 text-white font-bold py-2 px-6 rounded-lg
          hover:bg-amber-600 focus:outline-none focus:ring-2 focus:ring-amber-400
          transition duration-200
        "
      >
        Add to Cart
      </button>
    </div>
  );
}