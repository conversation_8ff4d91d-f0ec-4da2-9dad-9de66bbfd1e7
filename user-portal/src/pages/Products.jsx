import { useState } from "react";
import { useNavigate } from "react-router-dom";
import ProductCard from "../components/ProductCard.jsx";

export default function Products() {
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  // Use simple string paths starting with "/" to reference the `public` folder
  const DUMMY_PRODUCTS = [
    { id: "p1", name: "High-Performance Laptop", price: 158999, image: "/images/img1.png" }, // Corrected path
    { id: "p2", name: "Wireless Headphones", price: 5999, image: "/images/img2.png" },
    { id: "p3", name: "Iphone 2025", price: 74999, image: "/images/img3.png" },
    { id: "p4", name: "Mechanical Keyboard", price: 7999, image: "/images/img4.png" },
    { id: "p5", name: "Gaming Mouse", price: 3499, image: "/images/img5.png" },
    { id: "p6", name: "4K Monitor", price: 59999, image: "/images/img6.png" },
    { id: "p7", name: "Portable Speaker", price: 4999, image: "/images/img7.png" },
    { id: "p8", name: "Fitness Watch", price: 5999, image: "/images/img8.png" },
    { id: "p9", name: "Wireless Earbuds", price: 1599, image: "/images/img9.png" },
  ];

  const filteredProducts = DUMMY_PRODUCTS.filter((product) =>
    product.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleAddToCart = (product) => {
    const orderPayload = {
      user_id: "USER_101",
      items: [product.name],
      amount: product.price,
      image: product.image,
    };

    fetch("http://127.0.0.1:5001/place-order", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify(orderPayload),
    })
      .then((res) => res.json())
      .then((data) => {
        if (data.order_id) {
            alert(`Order placed for ${product.name}! Your Order ID is: ${data.order_id}`);
            navigate('/'); // Navigate to the orders page after placing an order
        } else {
            alert("Failed to place order.");
        }
      })
      .catch(error => {
          console.error("Error placing order:", error);
          alert("An error occurred while placing the order.");
      });
  };

  return (
    <div className="bg-gray-50 min-h-screen">
      {/* Header */}
      <div className="max-w-7xl mx-auto px-6 py-6 flex flex-col md:flex-row justify-between items-center gap-4">
        <h2 className="text-3xl font-bold text-gray-900">Shop Products</h2>
        <input
          type="text"
          placeholder="Search products..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full md:w-1/3 px-4 py-3 rounded-lg border border-gray-300 shadow-sm focus:ring-2 focus:ring-amber-400 outline-none transition"
        />
      </div>

      {/* Products Grid */}
      <div className="max-w-7xl mx-auto px-6 pb-12">
        {filteredProducts.length === 0 ? (
          <p className="text-gray-600 text-center mt-20 text-lg">No products found matching your search.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-8">
            {filteredProducts.map((p) => (
              <ProductCard key={p.id} product={p} onAddToCart={handleAddToCart} />
            ))}
          </div>
        )}
      </div>
    </div>
  );
}